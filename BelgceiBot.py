import io
import os
import tempfile
from pyrogram import Client,filters
from pyrogram.types import Message,InlineKeyboardButton, InlineKeyboardMarkup
from fpdf import FPDF
import pdfplumber
import fitz


api_id=29108471
api_hash="3c084bd30d228e5f40e98f298f3130a2"
bot_token="8174262200:AAG2HCNylHOgjTq3jfEdKARR8wTjwK_fDb0"

app=Client("belgeci_bot",api_id=api_id,api_hash=api_hash,bot_token=bot_token)
user_status={}


Select_markup = InlineKeyboardMarkup([
    [InlineKeyboardButton("تَحويل نَص إِلى pdf ", callback_data="text")],
    [InlineKeyboardButton("تَحويل pdf إِلى نَص", callback_data="pdf")],
    [InlineKeyboardButton("تَحويل صورة إِلى pdf ", callback_data="image")]
    ])

@app.on_message(filters.command("start") & filters.private)
async def start_handler(client: Client , message:Message):
    await message.reply_text(f' حَللِتَ اهلاً طيباً يا :  {message.from_user.username}  لُطفَا إِختر ما جِئتَ لِأَجلِه 🤍 ',reply_markup=Select_markup)

@app.on_callback_query(filters.regex("text")) 
async def handle_text_selection(client,callback_query):
    user_id=callback_query.from_user.id
    user_status[user_id] = "awaiting_text"
    await callback_query.message.reply_text("لُطفاً أرسِل نَصكَ وَ سَوفَ يَتِم تَحويلة 🤍")
    await callback_query.answer()

@app.on_message(filters.text & filters.private)
async def handle_text_t0_pdf(client , message : Message):
    user_id = message.from_user.id
    if user_status.get(user_id) == "awaiting_text":
      try:
        text = message.text
        
        pdf = FPDF()
        pdf.add_page()
        pdf.set_auto_page_break(auto=True, margin=15)
        pdf.set_margins(left=10, top=15, right=10)

        pdf.add_font("Universal", "", "NotoSans-Regular.ttf")
        pdf.set_font("Universal", size=13)

        for line in text.split("\n"):
            if line.strip():
                pdf.multi_cell(0, 10, line)

        pdf_buffer = io.BytesIO()
        pdf.output(pdf_buffer)
        pdf_buffer.seek(0)

        await message.reply_document(
            document=pdf_buffer,
            file_name="converted_text.pdf",
            caption="أمرُكَ قَد تَم 🖤"
        )
      finally:
        user_status.pop(user_id, None)




app.run()